import { supabase } from '../supabase/client';
import { TournamentRegistration } from '@/types/registration';

export interface CreateRegistrationParams {
  tournamentId: string;
  deckName?: string;
  archetypeId?: string; // Ora opzionale
  deckListUrl?: string;
}

export interface AdminCreatePlayerRegistrationParams {
  tournamentId: string;
  fullName: string;
  email: string; // Ora obbligatorio
  deckName?: string;
  archetypeId: string; // Ora obbligatorio
  deckListUrl: string; // Ora obbligatorio
}

export class RegistrationService {
  /**
   * Creates a new tournament registration for the authenticated user
   * Security: Uses RLS policies, validates user authentication
   */
  static async createRegistration(params: CreateRegistrationParams): Promise<TournamentRegistration> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User must be authenticated to register for tournaments');
    }

    // Get the player record for the authenticated user
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data: player, error: playerError } = await (supabase as any)
      .from('players')
      .select('id, onboarding_completed')
      .eq('auth_user_id', user.id)
      .single();

    if (playerError || !player) {
      throw new Error('Player profile not found. Please complete your profile first.');
    }

    if (!player.onboarding_completed) {
      throw new Error('Please complete your profile before registering for tournaments.');
    }

    // Check if user is already registered for this tournament
    const { data: existingRegistration, error: checkError } = await supabase
      .from('tournament_registrations')
      .select('id')
      .eq('tournament_id', params.tournamentId)
      .eq('player_id', player.id)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows returned
      throw new Error('Failed to check existing registration');
    }

    if (existingRegistration) {
      throw new Error('You are already registered for this tournament');
    }

    // Check tournament capacity
    const [tournamentResult, registrationsCountResult] = await Promise.all([
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (supabase as any)
        .from('tournaments')
        .select('max_players, title, date')
        .eq('id', params.tournamentId)
        .single(),
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (supabase as any)
        .from('tournament_registrations')
        .select('id', { count: 'exact', head: true })
        .eq('tournament_id', params.tournamentId)
    ]);

    if (tournamentResult.error) {
      throw new Error('Tournament not found');
    }

    const tournament = tournamentResult.data;
    const currentRegistrations = registrationsCountResult.count || 0;

    if (currentRegistrations >= tournament.max_players) {
      throw new Error('Tournament is full');
    }

    // Check if tournament is in the past (only tournaments from yesterday and before)
    // This allows users to register for tournaments happening later today
    const tournamentDate = new Date(tournament.date);
    const today = new Date();
    
    // Set today to start of day (00:00:00) for comparison
    // This ensures we're comparing dates, not timestamps
    const todayStartOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    
    // Set tournament date to start of day (00:00:00) for comparison
    const tournamentStartOfDay = new Date(tournamentDate.getFullYear(), tournamentDate.getMonth(), tournamentDate.getDate());
    
    // Only block registration if tournament is from yesterday or earlier
    // Tournaments scheduled for today (regardless of time) are still joinable
    if (tournamentStartOfDay < todayStartOfDay) {
      throw new Error('Cannot register for past tournaments');
    }

    // Validate archetype exists and is active (only if provided)
    if (params.archetypeId) {
      const { data: archetype, error: archetypeError } = await supabase
        .from('archetypes')
        .select('id, name')
        .eq('id', params.archetypeId)
        .eq('is_active', true)
        .single();

      if (archetypeError || !archetype) {
        throw new Error('Invalid or inactive archetype selected');
      }
    }

    // Create the registration
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data: registration, error: registrationError } = await (supabase as any)
      .from('tournament_registrations')
      .insert({
        tournament_id: params.tournamentId,
        player_id: player.id,
        deck_name: params.deckName || null,
        archetype_id: params.archetypeId || null,
        deck_list_url: params.deckListUrl || null,
        registration_date: new Date().toISOString()
      })
      .select(`
        id,
        tournament_id,
        player_id,
        registration_date,
        deck_name,
        archetype_id,
        deck_list_url,
        archetypes:archetype_id (
          id,
          name,
          description
        )
      `)
      .single();

    if (registrationError || !registration) {
      // Handle specific database errors
      if (registrationError?.code === '23505') { // Unique constraint violation
        throw new Error('You are already registered for this tournament');
      }
      throw new Error(`Registration failed: ${registrationError?.message || 'Unknown error'}`);
    }

    return registration as unknown as TournamentRegistration;
  }

  /**
   * Creates a new player and registers them for a tournament (Admin only)
   * This method creates a new player record and immediately registers them for the specified tournament
   * If a player with the same email already exists, it will register the existing player instead
   */
  static async adminCreatePlayerRegistration(params: AdminCreatePlayerRegistrationParams): Promise<TournamentRegistration> {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User must be authenticated');
    }

    // Verify user is admin
    const { data: isAdminResult, error: adminError } = await supabase.rpc('is_admin');
    if (adminError || !isAdminResult) {
      throw new Error('Only administrators can create player registrations');
    }

    // Validate tournament exists and get its details for date validation
    const { data: tournament, error: tournamentError } = await supabase
      .from('tournaments')
      .select('id, date, time_start')
      .eq('id', params.tournamentId)
      .single();

    if (tournamentError || !tournament) {
      throw new Error('Tournament not found');
    }

    // Validate required fields
    if (!params.email?.trim()) {
      throw new Error('Email is required');
    }
    if (!params.archetypeId?.trim()) {
      throw new Error('Archetype is required');
    }
    if (!params.deckListUrl?.trim()) {
      throw new Error('Deck list URL is required');
    }

    let playerId: string;

    // Check if a player with this email already exists
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data: existingPlayer, error: existingPlayerError } = await (supabase as any)
      .from('players')
      .select('id')
      .eq('email', params.email.trim())
      .single();

    if (existingPlayer && !existingPlayerError) {
      // Player exists, use their ID
      playerId = existingPlayer.id;

      // Check if player is already registered for this tournament
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const { data: existingRegistration, error: regCheckError } = await (supabase as any)
        .from('tournament_registrations')
        .select('id')
        .eq('tournament_id', params.tournamentId)
        .eq('player_id', playerId)
        .single();

      if (existingRegistration && !regCheckError) {
        throw new Error('This player is already registered for this tournament');
      }
    } else {
      // Player doesn't exist, create new one
      playerId = await this.createNewPlayer(params);
    }

    // Validate archetype exists and is active
    const { data: archetype, error: archetypeError } = await supabase
      .from('archetypes')
      .select('id, name')
      .eq('id', params.archetypeId)
      .eq('is_active', true)
      .single();

    if (archetypeError || !archetype) {
      throw new Error('Invalid or inactive archetype selected');
    }

    // Create the tournament registration
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data: registration, error: registrationError } = await (supabase as any)
      .from('tournament_registrations')
      .insert({
        tournament_id: params.tournamentId,
        player_id: playerId,
        deck_name: params.deckName?.trim() || null,
        archetype_id: params.archetypeId,
        deck_list_url: params.deckListUrl.trim(),
        registration_date: new Date().toISOString()
      })
      .select(`
        id,
        tournament_id,
        player_id,
        registration_date,
        deck_name,
        archetype_id,
        deck_list_url,
        archetypes:archetype_id (
          id,
          name,
          description
        )
      `)
      .single();

    if (registrationError || !registration) {
      throw new Error(`Failed to create registration: ${registrationError?.message || 'Unknown error'}`);
    }

    return registration;
  }

  /**
   * Helper method to create a new player
   */
  private static async createNewPlayer(params: AdminCreatePlayerRegistrationParams): Promise<string> {
    // Parse names from fullName
    const nameParts = params.fullName.trim().split(' ');
    const firstName = nameParts[0] || '';
    const lastName = nameParts.slice(1).join(' ') || '';

    // Create new player record
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data: newPlayer, error: playerError } = await (supabase as any)
      .from('players')
      .insert({
        name: params.fullName.trim(),
        email: params.email.trim(), // Email is now required
        first_name: firstName,
        last_name: lastName,
        onboarding_completed: true, // Admin-created players are considered onboarded
        auth_user_id: null // No auth user associated
      })
      .select('id')
      .single();

    if (playerError || !newPlayer) {
      throw new Error(`Failed to create player: ${playerError?.message || 'Unknown error'}`);
    }

    return newPlayer.id;
  }

  /**
   * Checks if the current user is registered for a tournament
   */
  static async checkRegistrationStatus(tournamentId: string): Promise<boolean> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return false;
    }

    // Get player ID
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data: player } = await (supabase as any)
      .from('players')
      .select('id')
      .eq('auth_user_id', user.id)
      .single();

    if (!player) {
      return false;
    }

    const { data: registration } = await supabase
      .from('tournament_registrations')
      .select('id')
      .eq('tournament_id', tournamentId)
      .eq('player_id', player.id)
      .single();

    return !!registration;
  }

  /**
   * Gets user's registration for a specific tournament
   */
  static async getUserRegistration(tournamentId: string): Promise<TournamentRegistration | null> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return null;
    }

    // Get player ID
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data: player } = await (supabase as any)
      .from('players')
      .select('id')
      .eq('auth_user_id', user.id)
      .single();

    if (!player) {
      return null;
    }

    const { data: registration } = await supabase
      .from('tournament_registrations')
      .select(`
        id,
        tournament_id,
        player_id,
        registration_date,
        deck_name,
        archetype_id,
        deck_list_url,
        archetypes:archetype_id (
          id,
          name,
          description
        )
      `)
      .eq('tournament_id', tournamentId)
      .eq('player_id', player.id)
      .single();

    return (registration as unknown as TournamentRegistration) || null;
  }

  /**
   * Gets all registrations for a tournament (admin/public use)
   * Optionally includes sensitive deck data for finished tournaments
   */
  static async getTournamentRegistrations(tournamentId: string, includeDeckData: boolean = true): Promise<TournamentRegistration[]> {
    // Base fields that are always safe to show
    let selectFields = `
      id,
      tournament_id,
      player_id,
      registration_date,
      archetype_id,
      players:player_id (
        id,
        name,
        first_name,
        last_name
      ),
      archetypes:archetype_id (
        id,
        name,
        description
      )
    `;
    
    // Add sensitive fields only if explicitly requested (e.g., for finished tournaments)
    if (includeDeckData) {
      selectFields = `
        id,
        tournament_id,
        player_id,
        registration_date,
        deck_name,
        archetype_id,
        deck_list_url,
        players:player_id (
          id,
          name,
          first_name,
          last_name
        ),
        archetypes:archetype_id (
          id,
          name,
          description
        )
      `;
    }

    const { data: registrations, error } = await supabase
      .from('tournament_registrations')
      .select(selectFields)
      .eq('tournament_id', tournamentId)
      .order('registration_date', { ascending: true });

    if (error) {
      throw new Error(`Failed to fetch tournament registrations: ${error.message}`);
    }

    return (registrations as unknown as TournamentRegistration[]) || [];
  }

  /**
   * Fetch all registrations of the authenticated user with tournament details
   * Security: relies on RLS and filters by current player's id
   */
  static async getMyRegistrations() {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data: player, error: playerError } = await (supabase as any)
      .from('players')
      .select('id')
      .eq('auth_user_id', user.id)
      .single();

    if (playerError || !player) throw new Error('Player profile not found');

    const { data, error } = await supabase
      .from('tournament_registrations')
      .select(`
        id,
        tournament_id,
        player_id,
        registration_date,
        deck_name,
        archetype_id,
        deck_list_url,
        deck_list,
        archetypes:archetype_id ( id, name ),
        tournament:tournament_id (
          id,
          title,
          date,
          time_start,
          time_end,
          format,
          price,
          store:stores ( id, name, color )
        )
      `)
      .eq('player_id', player.id)
      .order('registration_date', { ascending: false });

    if (error) throw new Error(`Failed to load your registrations: ${error.message}`);

    return data as unknown as import('@/types/registration').MyRegistrationWithTournament[];
  }

  /**
   * Update editable fields (deck_name, archetype_id, deck_list_url) for a user's own registration
   * Security-first: verifies auth, ownership, and ensures tournament hasn't started yet.
   */
  static async updateRegistration(params: {
    registrationId: string;
    deckName?: string | null;
    archetypeId?: string | null;
    deckListUrl?: string | null;
  }) {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    // Resolve player id
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data: player, error: playerError } = await (supabase as any)
      .from('players')
      .select('id')
      .eq('auth_user_id', user.id)
      .single();

    if (playerError || !player) throw new Error('Player profile not found');

    // Load registration with tournament timing to enforce time-based rule
    const { data: reg, error: loadError } = await supabase
      .from('tournament_registrations')
      .select(`
        id,
        player_id,
        tournament:tournament_id ( date, time_start )
      `)
      .eq('id', params.registrationId)
      .eq('player_id', player.id)
      .single();

    if (loadError || !reg) throw new Error('Registration not found');

    // Deny updates after the event start
    type RegWithTournamentStart = {
      id: string;
      player_id: string;
      tournament: { date: string; time_start: string };
    };
    const regTyped = reg as unknown as RegWithTournamentStart;
    const eventDate: Date = new Date(regTyped.tournament.date);
    const dateStr = eventDate.toISOString().split('T')[0];
    const start = new Date(`${dateStr}T${regTyped.tournament.time_start}`);
    if (new Date() >= start) {
      throw new Error('Le modifiche non sono consentite dopo l\'inizio dell\'evento');
    }

    // Optional: validate archetype exists if provided
    if (params.archetypeId) {
      const { data: archetype, error: aErr } = await supabase
        .from('archetypes')
        .select('id')
        .eq('id', params.archetypeId)
        .eq('is_active', true)
        .single();
      if (aErr || !archetype) {
        throw new Error('Archetipo non valido o non attivo');
      }
    }

    // Prepare safe payload
    type UpdatePayload = Partial<{
      deck_name: string | null;
      archetype_id: string | null;
      deck_list_url: string | null;
    }>;
    const payload: UpdatePayload = {};
    if (params.deckName !== undefined) payload.deck_name = params.deckName?.trim() || null;
    if (params.archetypeId !== undefined) payload.archetype_id = params.archetypeId || null;
    if (params.deckListUrl !== undefined) payload.deck_list_url = params.deckListUrl || null;

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data: updated, error: updateError } = await (supabase as any)
      .from('tournament_registrations')
      .update(payload)
      .eq('id', params.registrationId)
      .eq('player_id', player.id)
      .select(`
        id,
        tournament_id,
        player_id,
        registration_date,
        deck_name,
        archetype_id,
        deck_list_url,
        deck_list,
        archetypes:archetype_id ( id, name )
      `)
      .single();

    if (updateError) {
      throw new Error(`Aggiornamento non riuscito: ${updateError.message}`);
    }

    return updated as unknown as import('@/types/registration').TournamentRegistration;
  }

  /**
   * Deletes the authenticated user's own registration (irreversible)
   * Security-first: verifies auth, ownership, and ensures tournament hasn't started yet.
   */
  static async deleteRegistration(registrationId: string) {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    // Resolve player id
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data: player, error: playerError } = await (supabase as any)
      .from('players')
      .select('id')
      .eq('auth_user_id', user.id)
      .single();

    if (playerError || !player) throw new Error('Player profile not found');

    // Load registration with tournament timing
    const { data: reg, error: loadError } = await supabase
      .from('tournament_registrations')
      .select(`
        id,
        player_id,
        tournament_id,
        tournament:tournament_id ( date, time_start )
      `)
      .eq('id', registrationId)
      .eq('player_id', player.id)
      .single();

    if (loadError || !reg) throw new Error('Registration not found');

    type RegWithTournamentStart = {
      id: string;
      player_id: string;
      tournament_id: string;
      tournament: { date: string; time_start: string };
    };
    const regTyped = reg as unknown as RegWithTournamentStart;
    const eventDate: Date = new Date(regTyped.tournament.date);
    const dateStr = eventDate.toISOString().split('T')[0];
    const start = new Date(`${dateStr}T${regTyped.tournament.time_start}`);
    if (new Date() >= start) {
      throw new Error("Non è possibile cancellare l'iscrizione dopo l'inizio dell'evento");
    }

    const { error: delError } = await supabase
      .from('tournament_registrations')
      .delete()
      .eq('id', registrationId)
      .eq('player_id', player.id);

    if (delError) {
      throw new Error(`Cancellazione non riuscita: ${delError.message}`);
    }

    // Return the tournament ID so hooks can invalidate specific queries
    return { tournamentId: regTyped.tournament_id };
  }

  /**
   * Admin function to update any registration
   * Security: verifies admin status before allowing updates
   */
  static async adminUpdateRegistration(params: {
    registrationId: string;
    deckName?: string | null;
    archetypeId?: string | null;
    deckListUrl?: string | null;
  }) {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    // Verify user is admin
    const { data: isAdminResult, error: adminError } = await supabase.rpc('is_admin');
    if (adminError || !isAdminResult) {
      throw new Error('Only administrators can update registrations');
    }

    // Load registration to verify it exists
    const { data: reg, error: loadError } = await supabase
      .from('tournament_registrations')
      .select('id, tournament_id, player_id')
      .eq('id', params.registrationId)
      .single();

    if (loadError || !reg) throw new Error('Registration not found');

    // Optional: validate archetype exists if provided
    if (params.archetypeId) {
      const { data: archetype, error: aErr } = await supabase
        .from('archetypes')
        .select('id')
        .eq('id', params.archetypeId)
        .eq('is_active', true)
        .single();
      if (aErr || !archetype) {
        throw new Error('Archetipo non valido o non attivo');
      }
    }

    // Prepare safe payload
    type UpdatePayload = Partial<{
      deck_name: string | null;
      archetype_id: string | null;
      deck_list_url: string | null;
    }>;
    const payload: UpdatePayload = {};
    if (params.deckName !== undefined) payload.deck_name = params.deckName?.trim() || null;
    if (params.archetypeId !== undefined) payload.archetype_id = params.archetypeId || null;
    if (params.deckListUrl !== undefined) payload.deck_list_url = params.deckListUrl || null;

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data: updated, error: updateError } = await (supabase as any)
      .from('tournament_registrations')
      .update(payload)
      .eq('id', params.registrationId)
      .select(`
        id,
        tournament_id,
        player_id,
        registration_date,
        deck_name,
        archetype_id,
        deck_list_url,
        deck_list,
        archetypes:archetype_id ( id, name ),
        players:player_id ( id, name, first_name, last_name )
      `)
      .single();

    if (updateError) {
      throw new Error(`Aggiornamento non riuscito: ${updateError.message}`);
    }

    return updated as unknown as import('@/types/registration').TournamentRegistration;
  }

  /**
   * Admin function to delete any registration
   * Security: verifies admin status before allowing deletion
   */
  static async adminDeleteRegistration(registrationId: string) {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    // Verify user is admin
    const { data: isAdminResult, error: adminError } = await supabase.rpc('is_admin');
    if (adminError || !isAdminResult) {
      throw new Error('Only administrators can delete registrations');
    }

    // Load registration to get tournament ID for cache invalidation
    const { data: reg, error: loadError } = await supabase
      .from('tournament_registrations')
      .select('id, tournament_id')
      .eq('id', registrationId)
      .single();

    if (loadError || !reg) throw new Error('Registration not found');

    // Type the registration data
    const regTyped = reg as { id: string; tournament_id: string };

    const { error: delError } = await supabase
      .from('tournament_registrations')
      .delete()
      .eq('id', registrationId);

    if (delError) {
      throw new Error(`Cancellazione non riuscita: ${delError.message}`);
    }

    // Return the tournament ID so hooks can invalidate specific queries
    return { tournamentId: regTyped.tournament_id };
  }

  /**
   * Admin function to get a specific registration with full details
   * Security: verifies admin status before returning data
   */
  static async adminGetRegistration(registrationId: string): Promise<TournamentRegistration> {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    // Verify user is admin
    const { data: isAdminResult, error: adminError } = await supabase.rpc('is_admin');
    if (adminError || !isAdminResult) {
      throw new Error('Only administrators can view registration details');
    }

    const { data: registration, error } = await supabase
      .from('tournament_registrations')
      .select(`
        id,
        tournament_id,
        player_id,
        registration_date,
        deck_name,
        archetype_id,
        deck_list_url,
        deck_list,
        archetypes:archetype_id ( id, name, description ),
        players:player_id ( id, name, first_name, last_name )
      `)
      .eq('id', registrationId)
      .single();

    if (error || !registration) {
      throw new Error('Registration not found');
    }

    return registration as unknown as TournamentRegistration;
  }
}
