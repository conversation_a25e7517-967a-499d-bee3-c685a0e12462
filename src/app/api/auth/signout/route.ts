import { NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

export async function POST(request: Request) {
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    return NextResponse.json({ error: 'Missing Supabase env vars' }, { status: 500 });
  }

  // CSRF protection: enforce Origin/Host checks for cookie-authenticated POST
  try {
    const reqUrl = new URL(request.url);
    const originHeader = request.headers.get('origin');
    const refererHeader = request.headers.get('referer') || '';
    const hostHeader = request.headers.get('host');

    const currentOrigin = `${reqUrl.protocol}//${reqUrl.host}`;
    const configured = process.env.NEXT_PUBLIC_SITE_URL || currentOrigin;
    const baseOrigin = new URL(configured).origin;

    // Get allowed development origins from environment variable
    const getDevOrigins = (): string[] => {
      const envOrigins = process.env.ALLOWED_DEV_ORIGINS;
      if (envOrigins) {
        return envOrigins.split(',').map(origin => {
          const trimmed = origin.trim();
          // Ensure protocol is included for origins
          if (trimmed.includes(':') && !trimmed.startsWith('http')) {
            return `http://${trimmed}`;
          }
          return trimmed;
        });
      }

      // Default development origins
      return [
        'http://localhost:3000',
        'http://127.0.0.1:3000'
      ];
    };

    const allowedOrigins = new Set<string>([
      baseOrigin,
      currentOrigin,
      ...getDevOrigins(),
      // Add current hostname with port for development flexibility
      `http://${reqUrl.hostname}:3000`,
    ]);

    // Normalize origins (strip trailing slash)
    const normalize = (o: string) => o.replace(/\/$/, '');
    const hasAllowedOrigin = (o?: string | null) => !!o && allowedOrigins.has(normalize(o));

    if (originHeader) {
      if (!hasAllowedOrigin(originHeader)) {
        return NextResponse.json({ error: 'Invalid origin' }, { status: 403, headers: { Vary: 'Origin' } });
      }
    } else if (refererHeader) {
      try {
        const refererOrigin = new URL(refererHeader).origin;
        if (!hasAllowedOrigin(refererOrigin)) {
          return NextResponse.json({ error: 'Invalid referer' }, { status: 403, headers: { Vary: 'Referer' } });
        }
      } catch {
        return NextResponse.json({ error: 'Invalid referer' }, { status: 403, headers: { Vary: 'Referer' } });
      }
    }

    // Get allowed development hosts from environment variable
    const getDevHosts = (): string[] => {
      const envOrigins = process.env.ALLOWED_DEV_ORIGINS;
      if (envOrigins) {
        return envOrigins.split(',').map(origin => {
          const trimmed = origin.trim();
          // Extract host:port from origin
          if (trimmed.startsWith('http://') || trimmed.startsWith('https://')) {
            return new URL(trimmed).host;
          }
          return trimmed;
        });
      }

      // Default development hosts
      return [
        'localhost:3000',
        '127.0.0.1:3000'
      ];
    };

    // Allow common development hosts
    const allowedHosts = new Set([
      reqUrl.host, // The actual request host
      ...getDevHosts(),
    ]);

    if (hostHeader && !allowedHosts.has(hostHeader)) {
      return NextResponse.json({ error: 'Invalid host' }, { status: 403 });
    }
  } catch {}

  // Prepare a response object so that cookies set by Supabase are attached correctly
  const res = new NextResponse(null, { status: 204, headers: { 'Cache-Control': 'no-store' } });

  const cookieStore = await cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => {
            res.cookies.set({ name, value, ...options });
          });
        },
      },
    }
  );

  await supabase.auth.signOut();

  // Extra safety: explicitly clear any Supabase auth cookies present on the request
  try {
    // Clear typical cookie names used by supabase-js and @supabase/ssr
    const toClear = cookieStore.getAll()
      .map(c => c.name)
      .filter(name => name.startsWith('sb-') && (name.endsWith('-auth-token') || name.endsWith('-auth-token-code-verifier')));
    toClear.forEach(name => {
      res.cookies.set({ name, value: '', path: '/', maxAge: 0 });
    });
  } catch {}

  return res;
}
